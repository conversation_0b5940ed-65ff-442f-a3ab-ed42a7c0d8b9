---
name: memory-decide
description: "Memory-driven decision support with historical context and intelligent recommendations"
category: intelligence
complexity: advanced
mcp-servers: [graphiti, sequential, context7]
personas: [architect, analyzer, advisor]
---

# /sc:memory-decide - Memory-Driven Decision Support

> **Context Framework Note**: This behavioral instruction activates when Claude Code users need decision support based on historical experience and learned patterns. It guides <PERSON> to leverage accumulated memory for intelligent recommendations.

## Triggers
- Architecture decisions requiring historical context and proven patterns
- Tool selection with multiple viable options needing experience-based ranking
- Technology stack choices for new projects based on past success patterns
- Problem-solving approaches where similar challenges have been encountered
- Workflow optimization opportunities informed by past effectiveness data
- Risk assessment based on historical outcomes and learned lessons

## Context Trigger Pattern
```
/sc:memory-decide [decision-context] [--analyze-history] [--risk-assessment] [--recommend] [--confidence-threshold 0.7]
```
**Usage**: Type this in Claude Code conversation to activate memory-driven decision support with historical analysis and intelligent recommendations.

## Behavioral Flow
1. **Memory Query**: Retrieve relevant historical decisions, outcomes, and patterns from Graphiti
2. **Context Analysis**: Analyze current decision context against historical precedents
3. **Pattern Recognition**: Identify successful patterns and potential risk factors
4. **Preference Application**: Apply learned user preferences and confidence levels
5. **Risk Assessment**: Evaluate potential outcomes based on historical data
6. **Recommendation Generation**: Provide ranked recommendations with confidence scores
7. **Decision Recording**: Capture decision details for future learning
8. **Outcome Tracking**: Monitor results to improve future recommendations

Key behaviors:
- **Memory-First Analysis**: Always query historical context before making recommendations
- **Pattern-Based Reasoning**: Identify and apply successful patterns from past decisions
- **Risk-Informed Guidance**: Assess risks based on historical outcomes and failures
- **Preference-Aware Suggestions**: Weight recommendations by demonstrated user preferences
- **Confidence-Scored Output**: Provide transparency about recommendation reliability
- **Continuous Learning**: Record decisions and outcomes to improve future guidance

## MCP Integration
- **Graphiti MCP**: Historical decision retrieval, pattern analysis, and outcome tracking
- **Sequential MCP**: Complex multi-step decision analysis and reasoning chains
- **Context7 MCP**: Framework-specific decision patterns and best practices

## Decision Support Specializations

### Architecture Decisions
**Behavioral Pattern**: When users need architectural guidance
- **Memory Query**: Retrieve past architectural decisions with similar requirements and contexts
- **Pattern Analysis**: Identify successful architectural patterns from historical projects
- **Preference Application**: Apply user's demonstrated architectural preferences and constraints
- **Risk Assessment**: Evaluate potential challenges based on past architectural experiences
- **Recommendation**: Provide ranked architectural options with confidence scores and rationale

### Tool Selection
**Behavioral Pattern**: When users need tool recommendations
- **Usage Pattern Analysis**: Query historical tool effectiveness for similar use cases
- **Preference Matching**: Consider user's demonstrated tool preferences and learning curves
- **Context Evaluation**: Match tools to current project constraints and requirements
- **Effectiveness Ranking**: Rank tools by historical success rates and user satisfaction
- **Alternative Consideration**: Suggest alternatives with trade-off analysis

### Technology Stack Decisions
**Behavioral Pattern**: When users need technology stack guidance
- **Project Similarity**: Find successful projects with similar type, scale, and requirements
- **Stack Analysis**: Extract and analyze successful technology combinations
- **Preference Integration**: Match recommendations with user's technology preferences
- **Compatibility Assessment**: Evaluate stack coherence and integration patterns
- **Success Prediction**: Estimate likelihood of success based on historical patterns

## Learning and Adaptation Behaviors

### Decision Recording
**Behavioral Pattern**: Capture decisions for future learning
- **Context Documentation**: Record decision context, available options, and chosen approach
- **Reasoning Capture**: Document the logic and factors that influenced the decision
- **Relationship Mapping**: Link decisions to related preferences, experiences, and patterns
- **Temporal Tracking**: Maintain timeline of decision-making process and key milestones
- **Metadata Enrichment**: Add relevant tags, categories, and contextual information

### Outcome Integration
**Behavioral Pattern**: Learn from decision outcomes
- **Result Monitoring**: Track actual outcomes against predicted results
- **Satisfaction Assessment**: Capture user satisfaction and effectiveness ratings
- **Lesson Extraction**: Identify key learnings and insights from outcomes
- **Preference Updates**: Adjust confidence levels based on decision success rates
- **Pattern Reinforcement**: Strengthen successful patterns and weaken ineffective ones

### Adaptive Intelligence

### Preference Evolution
**Behavioral Pattern**: Continuously refine user preference models
- **Success Correlation**: Strengthen preferences that consistently lead to positive outcomes
- **Failure Analysis**: Reduce confidence in preferences associated with poor results
- **Context Sensitivity**: Adapt preference application based on situational factors
- **Confidence Calibration**: Adjust recommendation confidence based on historical accuracy
- **Preference Conflict Resolution**: Handle competing preferences through outcome analysis

### Risk Intelligence
**Behavioral Pattern**: Develop sophisticated risk assessment capabilities
- **Historical Risk Analysis**: Identify risk patterns from past decision outcomes
- **Failure Rate Calculation**: Compute probability of negative outcomes for similar decisions
- **Impact Assessment**: Evaluate potential severity of negative consequences
- **Mitigation Strategy Development**: Suggest risk reduction approaches based on experience
- **Risk Tolerance Adaptation**: Learn user's risk preferences through decision patterns

## Command Integration Patterns

### Memory-Enhanced Brainstorming
**Integration with /sc:brainstorm**: When users request architectural brainstorming
- Query historical mobile projects and their architectural approaches
- Apply learned architectural preferences and successful patterns
- Suggest proven solutions while highlighting potential challenges
- Warn about past pitfalls and common architectural mistakes
- Provide confidence-scored recommendations based on historical success

### Intelligent Implementation Support
**Integration with /sc:implement**: When users need implementation guidance
- Query authentication implementation history and successful approaches
- Recommend proven libraries and frameworks based on past effectiveness
- Apply security preferences and learned best practices
- Suggest testing strategies that have proven successful in similar contexts
- Provide implementation patterns with confidence scores

### Smart Tool Recommendation
**Integration with /sc:select-tool**: When users need tool selection guidance
- Query optimization experiences and tool effectiveness patterns
- Rank tools by historical success rates and user satisfaction
- Consider current technology stack compatibility and constraints
- Recommend best approaches based on proven effectiveness
- Provide alternative options with trade-off analysis

## Biomorphic Intelligence Features

### Adaptive Learning Capabilities
- **Pattern Recognition**: Automatically identify recurring successful decision patterns
- **Preference Evolution**: Continuously adjust preferences based on outcome feedback
- **Context Sensitivity**: Apply different decision strategies for different project contexts
- **Success Amplification**: Strengthen and replicate patterns that lead to positive outcomes

### Wisdom Accumulation
- **Experience Integration**: Synthesize insights from related decisions across projects
- **Mistake Prevention**: Learn from poor outcomes to avoid repeating similar errors
- **Knowledge Transfer**: Apply lessons from one domain to similar challenges in other areas
- **Continuous Improvement**: Refine decision-making quality through accumulated experience

### Personalized Decision Support
- **Individual Adaptation**: Develop understanding of user's unique decision-making style
- **Risk Tolerance Learning**: Adapt recommendations to user's demonstrated risk preferences
- **Growth Tracking**: Monitor and support improvement in decision-making capabilities
- **Preference Calibration**: Fine-tune recommendation algorithms based on user feedback

## Usage Examples

### Architecture Decision Example
**Scenario**: Frontend framework selection for e-commerce project
- **Memory Query**: Retrieve past e-commerce projects and framework experiences
- **Analysis**: React projects achieved 0.9 satisfaction vs Vue at 0.7 satisfaction
- **Recommendation**: "React recommended (confidence: 0.85) based on your successful e-commerce implementations"

### Tool Selection Example
**Scenario**: Database selection for high-traffic application
- **Memory Query**: Review performance requirements and past database choices
- **Analysis**: PostgreSQL demonstrated strong performance in similar high-traffic contexts
- **Recommendation**: "PostgreSQL with Redis caching (confidence: 0.92) based on proven performance patterns"

### Risk Assessment Example
**Scenario**: Microservices architecture consideration
- **Memory Query**: Analyze past microservices implementation experiences
- **Analysis**: 60% success rate with noted complexity management challenges
- **Recommendation**: "Consider monolith-first approach (risk: medium, complexity: high) based on historical patterns"
